package flink.common;

import flink.constant.Constants;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.core.fs.FSDataInputStream;
import org.apache.flink.core.fs.FileSystem;
import org.apache.flink.core.fs.Path;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.Serializable;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Properties;

public class ConfigurationManager implements Serializable {
    private static Logger log = LoggerFactory.getLogger(ConfigurationManager.class);

    ParameterTool params;
    Properties prop = new Properties();

    public ConfigurationManager(String[] args) throws URISyntaxException, IOException {
        params = ParameterTool.fromArgs(args);
        String s3ConfigPath = params.get(Constants.S3_CONFIG_PATH);

        log.error(params.get(Constants.S3_CONFIG_PATH));
//        String s3ConfigPath = "s3://canary-lb-bi-ci/flink/properties/lb/test0113.properties";
        try {
            URI s3uri = new URI(s3ConfigPath);
            FileSystem fs = FileSystem.get(s3uri);
            Path inFile = new Path(s3ConfigPath);
            FSDataInputStream in = fs.open(inFile);
            prop.load(in);
        } catch (URISyntaxException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } catch (IllegalArgumentException e) {
            e.printStackTrace();
        }
    }

    public String getString(String key) {
        String value = params.get(key);
        if (null == value) {
            value = prop.getProperty(key);
        }
        return value;
    }


    public Integer getInteger(String key) {
        String value = getString(key);
        try {
            return Integer.valueOf(value);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0;
    }


    public Boolean getBoolean(String key) {
        String value = getString(key);
        try {
            return Boolean.valueOf(value);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }


    public Long getLong(String key) {
        String value = getString(key);
        try {
            return Long.valueOf(value);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0L;
    }

}
